# 优化版本碳化硅外延层厚度测定程序依赖包
# 安装命令: pip install -r requirements_optimized.txt

# 核心科学计算库
numpy>=1.19.0
scipy>=1.5.0
pandas>=1.1.0

# 绘图和可视化
matplotlib>=3.3.0

# 数值优化（scipy已包含，但明确列出以确保版本）
# scipy.optimize 用于参数优化

# 可选：性能优化库
# numba>=0.50.0  # 可选，用于加速计算
# joblib>=0.16.0  # 可选，用于并行计算

# 开发和测试工具（可选）
# pytest>=6.0.0  # 用于单元测试
# jupyter>=1.0.0  # 用于交互式分析

# 文档生成（可选）
# sphinx>=3.0.0  # 用于生成文档

# 数据处理增强（可选）
# openpyxl>=3.0.0  # 用于Excel文件读写
# xlsxwriter>=1.3.0  # 用于Excel文件写入

# 注意事项:
# 1. 建议使用Python 3.7或更高版本
# 2. 如果遇到编码问题，确保系统支持中文字符集
# 3. 在Windows系统上，可能需要安装Microsoft Visual C++ Build Tools
# 4. 如果matplotlib显示中文有问题，请安装中文字体

# 最小化安装（仅核心功能）:
# pip install numpy scipy pandas matplotlib

# 完整安装（包含所有功能）:
# pip install -r requirements_optimized.txt
