# 问题 1：碳化硅外延层厚度确定

## 项目简介

本项目实现了基于红外干涉法的碳化硅外延层厚度测定算法，采用快速傅里叶变换(FFT)方法从干涉光谱数据中提取光程差信息，进而准确计算外延层厚度。

## 文件结构

```
代码/
├── problem1_solution.py    # 主程序文件
├── requirements.txt        # 依赖包列表
├── problem1_results.md     # 计算结果报告
├── README.md              # 使用说明
└── data/                  # 数据文件夹
    ├── 附件1.csv          # 10°入射角数据
    ├── 附件2.csv          # 15°入射角数据
    ├── 附件3.csv          # 硅晶圆片10°数据
    └── 附件4.csv          # 硅晶圆片15°数据
```

## 环境要求

- Python 3.7+
- 必需的库：numpy, scipy, pandas, matplotlib

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行方法

```bash
python problem1_solution.py
```

## 输出结果

运行程序后，会自动在当前目录下创建 `results/` 文件夹，包含以下文件：

- `problem1_analysis_10deg_thickness_XX.Xum.png/pdf` - 10° 入射角分析图
- `problem1_analysis_15deg_thickness_XX.Xum.png/pdf` - 15° 入射角分析图
- `problem1_summary_report_avg_XX.Xum.png/pdf` - 综合对比报告图
- `图片说明.md` - 图片文件说明

所有图片都保存为高分辨率（300 DPI）的 PNG 和 PDF 两种格式。

## 核心功能

1. **数据读取**：自动处理 CSV 格式的光谱数据，支持多种编码格式
2. **数据预处理**：线性插值生成均匀采样数据，满足 FFT 算法要求
3. **FFT 分析**：提取干涉信号的主频成分，获得光程差信息
4. **厚度计算**：基于光学干涉理论计算外延层厚度
5. **可靠性验证**：交叉验证不同入射角的计算结果
6. **结果可视化**：生成光谱图和 FFT 分析图表

## 算法原理

### 数学模型

基于薄膜双光束干涉理论：

```
2d * sqrt(n1^2 - sin^2(θi)) = (m + 1/2) / νm
```

### FFT 算法优势

- 避免求解未知干涉级次 m
- 利用全谱信息提高精度
- 自动化程度高，抗噪声能力强

## 计算结果

对碳化硅晶圆片的分析结果：

- **外延层厚度**：35.66 ± 0.05 μm
- **计算精度**：相对误差 0.28%
- **可靠性**：优秀（两个入射角结果高度一致）

## 技术特点

1. **高精度**：相对误差小于 1%
2. **高可靠性**：多角度验证确保结果准确
3. **自动化**：全程自动处理，无需人工干预
4. **通用性**：可适用于各种半导体外延层厚度测量

## 适用范围

- 碳化硅(SiC)外延层厚度测定
- 其他半导体材料外延层分析（需调整折射率参数）
- 薄膜厚度的光学干涉测量

## 注意事项

1. 确保数据文件放在 data/文件夹中
2. CSV 文件应包含波数和反射率两列数据
3. 可根据具体材料调整折射率参数
