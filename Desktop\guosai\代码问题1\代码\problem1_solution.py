# -*- coding: utf-8 -*-
"""
问题1：碳化硅外延层厚度确定的数学建模与算法实现

基于红外干涉法的双光束干涉原理，使用快速傅里叶变换(FFT)算法
从干涉光谱数据中提取光程差信息，进而计算外延层厚度。

核心数学模型：
2d * sqrt(n1^2 - sin^2(θi)) = (m + 1/2) / νm

其中：
- d: 外延层厚度（待求）
- n1: 外延层折射率
- θi: 入射角
- νm: 第m级干涉峰对应的波数
- m: 干涉级次

算法采用FFT方法直接提取光程差，避免求解未知整数m的困难。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
from scipy.fft import fft, fftfreq
import os

# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def load_spectral_data(file_path: str) -> tuple:
    """
    从CSV文件中加载光谱数据。
    
    参数:
    file_path (str): CSV文件的路径
    
    返回:
    tuple[np.ndarray, np.ndarray]: 包含波数 (cm-1) 和反射率 (%) 的元组
    """
    try:
        # 尝试不同的编码方式读取CSV文件
        encodings = ['gbk', 'gb2312', 'utf-8', 'latin-1']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            raise ValueError("无法使用任何编码读取文件")
        
        # 获取列名
        columns = df.columns.tolist()
        print(f"文件 {os.path.basename(file_path)} 的列名: {columns}")
        
        # 提取波数和反射率数据
        wavenumber = df.iloc[:, 0].to_numpy()  # 第一列：波数
        reflectance = df.iloc[:, 1].to_numpy()  # 第二列：反射率
        
        # 移除可能的无效数据
        valid_mask = ~(np.isnan(wavenumber) | np.isnan(reflectance))
        wavenumber = wavenumber[valid_mask]
        reflectance = reflectance[valid_mask]
        
        print(f"成功加载数据：{len(wavenumber)} 个数据点")
        print(f"波数范围：{wavenumber.min():.2f} - {wavenumber.max():.2f} cm⁻¹")
        print(f"反射率范围：{reflectance.min():.2f} - {reflectance.max():.2f} %")
        
        return wavenumber, reflectance
        
    except Exception as e:
        print(f"错误：无法读取文件 {file_path}")
        print(f"错误信息：{e}")
        return None, None


def preprocess_and_interpolate(wavenumber: np.ndarray, 
                               reflectance: np.ndarray, 
                               num_points: int = 2**16) -> tuple:
    """
    对光谱数据进行预处理，通过线性插值得到均匀间隔的数据点。
    
    参数:
    wavenumber (np.ndarray): 原始波数数据
    reflectance (np.ndarray): 原始反射率数据
    num_points (int): 插值后的数据点数量，建议为2的幂次方
    
    返回:
    tuple[np.ndarray, np.ndarray]: 包含均匀波数网格和对应插值反射率的元组
    """
    # 创建线性插值函数
    interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                          bounds_error=False, fill_value='extrapolate')
    
    # 创建均匀间隔的波数网格
    uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), num_points)
    
    # 计算对应的反射率
    uniform_reflectance = interp_func(uniform_wavenumber)
    
    print(f"插值完成：{len(uniform_wavenumber)} 个均匀数据点")
    print(f"波数步长：{uniform_wavenumber[1] - uniform_wavenumber[0]:.4f} cm⁻¹")
    
    return uniform_wavenumber, uniform_reflectance


def calculate_opd_from_fft(uniform_wavenumber: np.ndarray, 
                           uniform_reflectance: np.ndarray) -> tuple:
    """
    通过FFT计算光程差 (Optical Path Difference, OPD)。
    
    参数:
    uniform_wavenumber (np.ndarray): 均匀间隔的波数网格
    uniform_reflectance (np.ndarray): 插值后的反射率数据
    
    返回:
    tuple[float, np.ndarray, np.ndarray]: 
        - 计算出的光程差 (OPD) 值，单位 cm
        - OPD轴 (cm)
        - FFT幅度谱
    """
    # 计算采样参数
    N = len(uniform_wavenumber)
    wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
    
    # 去除直流分量，对反射率数据执行FFT
    reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
    reflectance_fft = fft(reflectance_centered)
    
    # 计算FFT幅度谱
    fft_magnitude = np.abs(reflectance_fft)
    
    # 计算FFT频率轴（对应光程差）
    opd_axis = fftfreq(N, d=wavenumber_step)
    
    # 只考虑正的光程差
    positive_opd_axis = opd_axis[:N // 2]
    positive_fft_magnitude = fft_magnitude[:N // 2]
    
    # 寻找主峰位置（忽略零频分量）
    # 排除前几个点以避免低频噪声
    start_idx = max(1, int(0.001 * N))  # 排除前0.1%的点
    peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
    
    # 获取峰值对应的OPD
    opd_value = positive_opd_axis[peak_index]
    peak_magnitude = positive_fft_magnitude[peak_index]
    
    print(f"FFT分析完成")
    print(f"检测到的主峰位置：{opd_value:.6f} cm")
    print(f"主峰幅度：{peak_magnitude:.2f}")
    print(f"OPD分辨率：{positive_opd_axis[1] - positive_opd_axis[0]:.8f} cm")
    
    return opd_value, positive_opd_axis, positive_fft_magnitude


def calculate_thickness(opd: float, n1: float, theta_i_deg: float) -> float:
    """
    根据光程差、折射率和入射角计算外延层厚度。
    
    基于数学模型：L = 2d * sqrt(n1^2 - sin^2(θi))
    因此：d = L / (2 * sqrt(n1^2 - sin^2(θi)))
    
    参数:
    opd (float): 从FFT计算得到的光程差，单位 cm
    n1 (float): 外延层的折射率
    theta_i_deg (float): 入射角，单位 度 (°)
    
    返回:
    float: 计算出的外延层厚度，单位 微米 (μm)
    """
    # 将入射角从度转换为弧度
    theta_i_rad = np.deg2rad(theta_i_deg)
    
    # 应用数学模型公式
    # d = L / (2 * sqrt(n1^2 - sin^2(θi)))
    numerator = opd
    denominator = 2 * np.sqrt(n1**2 - np.sin(theta_i_rad)**2)
    
    # 计算厚度，单位为 cm
    thickness_cm = numerator / denominator
    
    # 转换为微米 (1 cm = 10000 μm)
    thickness_um = thickness_cm * 1e4
    
    print(f"厚度计算：")
    print(f"  光程差 L = {opd:.6f} cm")
    print(f"  折射率 n1 = {n1}")
    print(f"  入射角 θi = {theta_i_deg}°")
    print(f"  计算厚度 d = {thickness_um:.3f} μm")
    
    return thickness_um


def plot_results(wavenumber, reflectance, uniform_wavenumber, uniform_reflectance,
                opd_axis, fft_magnitude, opd_value, title_suffix="", save_path=None):
    """
    绘制分析结果的可视化图表，并保存到本地。
    
    参数:
    wavenumber: 原始波数数据
    reflectance: 原始反射率数据
    uniform_wavenumber: 插值后的波数数据
    uniform_reflectance: 插值后的反射率数据
    opd_axis: 光程差轴
    fft_magnitude: FFT幅度谱
    opd_value: 检测到的光程差值
    title_suffix: 图表标题后缀
    save_path: 图片保存路径（不含扩展名）
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'问题1：碳化硅外延层厚度分析{title_suffix}', fontsize=16, fontweight='bold')
    
    # 子图1: 原始光谱
    axes[0, 0].plot(wavenumber, reflectance, 'b-', linewidth=1, label='原始数据')
    axes[0, 0].set_title('原始干涉光谱')
    axes[0, 0].set_xlabel('波数 (cm^-1)')  # 使用^-1代替⁻¹
    axes[0, 0].set_ylabel('反射率 (%)')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].legend()
    
    # 子图2: 插值后的光谱
    axes[0, 1].plot(uniform_wavenumber, uniform_reflectance, 'r-', linewidth=1, label='插值数据')
    axes[0, 1].set_title('插值后的均匀光谱')
    axes[0, 1].set_xlabel('波数 (cm^-1)')  # 使用^-1代替⁻¹
    axes[0, 1].set_ylabel('反射率 (%)')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].legend()
    
    # 子图3: FFT幅度谱（全范围）
    axes[1, 0].plot(opd_axis, fft_magnitude, 'g-', linewidth=1)
    axes[1, 0].axvline(opd_value, color='r', linestyle='--', linewidth=2, 
                      label=f'检测到的OPD = {opd_value:.4f} cm')
    axes[1, 0].set_title('FFT幅度谱（光程差域）')
    axes[1, 0].set_xlabel('光程差 (cm)')
    axes[1, 0].set_ylabel('幅度')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].legend()
    
    # 子图4: FFT幅度谱（局部放大）
    max_opd_display = min(opd_value * 3, opd_axis.max())
    mask = opd_axis <= max_opd_display
    axes[1, 1].plot(opd_axis[mask], fft_magnitude[mask], 'g-', linewidth=2)
    axes[1, 1].axvline(opd_value, color='r', linestyle='--', linewidth=2, 
                      label=f'主峰位置 = {opd_value:.4f} cm')
    axes[1, 1].set_title('FFT幅度谱（局部放大）')
    axes[1, 1].set_xlabel('光程差 (cm)')
    axes[1, 1].set_ylabel('幅度')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].legend()
    
    plt.tight_layout()
    
    # 保存图片到本地
    if save_path:
        try:
            # 确保保存目录存在
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)
            
            # 保存为高分辨率PNG图片
            plt.savefig(save_path + '.png', dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            
            # 同时保存为PDF格式（可选）
            plt.savefig(save_path + '.pdf', bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            
            print(f"图片已保存：")
            print(f"  PNG格式：{save_path}.png")
            print(f"  PDF格式：{save_path}.pdf")
            
        except Exception as e:
            print(f"保存图片失败：{e}")
    
    plt.show()


def create_summary_plot(results, save_path=None):
    """
    创建综合结果对比图表。
    
    参数:
    results: 包含两个入射角结果的字典
    save_path: 保存路径（不含扩展名）
    """
    if '10deg' not in results or '15deg' not in results:
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('问题1：碳化硅外延层厚度测定 - 综合结果对比', fontsize=16, fontweight='bold')
    
    # 获取数据
    w_10, r_10, uw_10, ur_10, opd_axis_10, fft_mag_10 = results['10deg']['data']
    w_15, r_15, uw_15, ur_15, opd_axis_15, fft_mag_15 = results['15deg']['data']
    opd_10, thickness_10 = results['10deg']['opd'], results['10deg']['thickness']
    opd_15, thickness_15 = results['15deg']['opd'], results['15deg']['thickness']
    
    # 子图1: 原始光谱对比
    axes[0, 0].plot(w_10, r_10, 'b-', linewidth=1, label=f'10° 入射角', alpha=0.8)
    axes[0, 0].plot(w_15, r_15, 'r-', linewidth=1, label=f'15° 入射角', alpha=0.8)
    axes[0, 0].set_title('原始干涉光谱对比')
    axes[0, 0].set_xlabel('波数 (cm^-1)')  # 使用^-1代替⁻¹
    axes[0, 0].set_ylabel('反射率 (%)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 子图2: FFT幅度谱对比
    max_opd = max(opd_10, opd_15) * 3
    mask_10 = opd_axis_10 <= max_opd
    mask_15 = opd_axis_15 <= max_opd
    
    axes[0, 1].plot(opd_axis_10[mask_10], fft_mag_10[mask_10], 'b-', 
                   linewidth=1.5, label=f'10° (OPD={opd_10:.4f} cm)', alpha=0.8)
    axes[0, 1].plot(opd_axis_15[mask_15], fft_mag_15[mask_15], 'r-', 
                   linewidth=1.5, label=f'15° (OPD={opd_15:.4f} cm)', alpha=0.8)
    axes[0, 1].axvline(opd_10, color='b', linestyle='--', alpha=0.7)
    axes[0, 1].axvline(opd_15, color='r', linestyle='--', alpha=0.7)
    axes[0, 1].set_title('FFT幅度谱对比')
    axes[0, 1].set_xlabel('光程差 (cm)')
    axes[0, 1].set_ylabel('幅度')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 子图3: 厚度计算结果
    angles = ['10°', '15°']
    thicknesses = [thickness_10, thickness_15]
    colors = ['blue', 'red']
    
    bars = axes[1, 0].bar(angles, thicknesses, color=colors, alpha=0.7, width=0.5)
    axes[1, 0].set_title('外延层厚度计算结果')
    axes[1, 0].set_ylabel('厚度 (μm)')
    axes[1, 0].grid(True, alpha=0.3, axis='y')
    
    # 在柱状图上标注数值
    for bar, thickness in zip(bars, thicknesses):
        height = bar.get_height()
        axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{thickness:.3f} μm', ha='center', va='bottom', fontweight='bold')
    
    # 添加平均线
    avg_thickness = (thickness_10 + thickness_15) / 2
    axes[1, 0].axhline(avg_thickness, color='green', linestyle='--', 
                      label=f'平均值: {avg_thickness:.3f} μm')
    axes[1, 0].legend()
    
    # 子图4: 误差分析
    relative_error = abs(thickness_10 - thickness_15) / avg_thickness * 100
    
    # 创建误差条形图
    axes[1, 1].bar(['相对误差'], [relative_error], color='orange', alpha=0.7, width=0.3)
    axes[1, 1].axhline(5.0, color='red', linestyle='--', label='5% 警戒线')
    axes[1, 1].axhline(1.0, color='green', linestyle='--', label='1% 优秀线')
    axes[1, 1].set_title('结果可靠性分析')
    axes[1, 1].set_ylabel('相对误差 (%)')
    axes[1, 1].grid(True, alpha=0.3, axis='y')
    axes[1, 1].legend()
    
    # 在误差柱上标注数值
    axes[1, 1].text(0, relative_error + 0.05, f'{relative_error:.2f}%', 
                   ha='center', va='bottom', fontweight='bold')
    
    # 添加评估文本
    if relative_error < 1.0:
        assessment = "优秀"
        color = 'green'
    elif relative_error < 5.0:
        assessment = "良好"
        color = 'orange'
    else:
        assessment = "需改进"
        color = 'red'
    
    axes[1, 1].text(0.5, 0.95, f'可靠性评估: {assessment}', 
                   transform=axes[1, 1].transAxes, ha='center', va='top',
                   bbox=dict(boxstyle='round', facecolor=color, alpha=0.3),
                   fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    
    # 保存综合报告图
    if save_path:
        try:
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)
            
            plt.savefig(save_path + '.png', dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.savefig(save_path + '.pdf', bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            
            print(f"综合报告图已保存：")
            print(f"  PNG格式：{save_path}.png")
            print(f"  PDF格式：{save_path}.pdf")
            
        except Exception as e:
            print(f"保存综合报告图失败：{e}")
    
    plt.show()


def main():
    """
    主程序：实现问题1的完整解决方案
    """
    print("="*60)
    print("问题1：碳化硅外延层厚度确定")
    print("基于红外干涉法的FFT算法实现")
    print("="*60)
    
    # 文件路径配置
    data_dir = "data"
    file_path_10_deg = os.path.join(data_dir, "附件1.csv")  # 10°入射角数据
    file_path_15_deg = os.path.join(data_dir, "附件2.csv")  # 15°入射角数据
    
    # 材料参数
    # 碳化硅(SiC)在红外波段的平均折射率
    # 参考文献值：约2.55-2.60，这里取中间值
    N1_SIC_AVG = 2.58
    
    print(f"\n使用的材料参数：")
    print(f"碳化硅(SiC)平均折射率：{N1_SIC_AVG}")
    
    # 结果存储
    results = {}
    
    # 处理10°入射角数据
    print("\n" + "="*50)
    print("正在处理附件1 (入射角 10°)")
    print("="*50)
    
    w_10, r_10 = load_spectral_data(file_path_10_deg)
    if w_10 is not None:
        # 数据预处理
        uw_10, ur_10 = preprocess_and_interpolate(w_10, r_10)
        
        # FFT分析
        opd_10, opd_axis_10, fft_mag_10 = calculate_opd_from_fft(uw_10, ur_10)
        
        # 计算厚度
        thickness_10 = calculate_thickness(opd_10, N1_SIC_AVG, 10.0)
        
        # 存储结果
        results['10deg'] = {
            'opd': opd_10,
            'thickness': thickness_10,
            'data': (w_10, r_10, uw_10, ur_10, opd_axis_10, fft_mag_10)
        }
        
        print(f"\n✓ 10°入射角分析完成")
        print(f"  光程差：{opd_10:.6f} cm")
        print(f"  外延层厚度：{thickness_10:.3f} μm")
    
    # 处理15°入射角数据
    print("\n" + "="*50)
    print("正在处理附件2 (入射角 15°)")
    print("="*50)
    
    w_15, r_15 = load_spectral_data(file_path_15_deg)
    if w_15 is not None:
        # 数据预处理
        uw_15, ur_15 = preprocess_and_interpolate(w_15, r_15)
        
        # FFT分析
        opd_15, opd_axis_15, fft_mag_15 = calculate_opd_from_fft(uw_15, ur_15)
        
        # 计算厚度
        thickness_15 = calculate_thickness(opd_15, N1_SIC_AVG, 15.0)
        
        # 存储结果
        results['15deg'] = {
            'opd': opd_15,
            'thickness': thickness_15,
            'data': (w_15, r_15, uw_15, ur_15, opd_axis_15, fft_mag_15)
        }
        
        print(f"\n✓ 15°入射角分析完成")
        print(f"  光程差：{opd_15:.6f} cm")
        print(f"  外延层厚度：{thickness_15:.3f} μm")
    
    # 可靠性分析
    if '10deg' in results and '15deg' in results:
        print("\n" + "="*60)
        print("结果可靠性分析")
        print("="*60)
        
        thickness_10 = results['10deg']['thickness']
        thickness_15 = results['15deg']['thickness']
        
        # 计算平均值和相对误差
        avg_thickness = (thickness_10 + thickness_15) / 2
        relative_error = abs(thickness_10 - thickness_15) / avg_thickness * 100
        
        print(f"10°入射角计算厚度：{thickness_10:.3f} μm")
        print(f"15°入射角计算厚度：{thickness_15:.3f} μm")
        print(f"平均厚度：{avg_thickness:.3f} μm")
        print(f"相对误差：{relative_error:.2f} %")
        
        # 可靠性评估
        if relative_error < 5.0:
            print(f"\n✓ 结果可靠性评估：优秀（相对误差 < 5%）")
        elif relative_error < 10.0:
            print(f"\n✓ 结果可靠性评估：良好（相对误差 < 10%）")
        else:
            print(f"\n⚠ 结果可靠性评估：需要进一步验证（相对误差 ≥ 10%）")
    
    # 可视化结果并保存图片
    print("\n" + "="*40)
    print("生成可视化图表并保存到本地")
    print("="*40)
    
    # 创建结果输出目录
    output_dir = "results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录：{output_dir}")
    
    if '10deg' in results:
        w_10, r_10, uw_10, ur_10, opd_axis_10, fft_mag_10 = results['10deg']['data']
        opd_10 = results['10deg']['opd']
        thickness_10 = results['10deg']['thickness']
        
        save_path_10 = os.path.join(output_dir, f"problem1_analysis_10deg_thickness_{thickness_10:.1f}um")
        plot_results(w_10, r_10, uw_10, ur_10, opd_axis_10, fft_mag_10, 
                    opd_10, " (入射角 10°)", save_path=save_path_10)
    
    if '15deg' in results:
        w_15, r_15, uw_15, ur_15, opd_axis_15, fft_mag_15 = results['15deg']['data']
        opd_15 = results['15deg']['opd']
        thickness_15 = results['15deg']['thickness']
        
        save_path_15 = os.path.join(output_dir, f"problem1_analysis_15deg_thickness_{thickness_15:.1f}um")
        plot_results(w_15, r_15, uw_15, ur_15, opd_axis_15, fft_mag_15, 
                    opd_15, " (入射角 15°)", save_path=save_path_15)
    
    # 生成综合对比报告图
    if '10deg' in results and '15deg' in results:
        print("\n" + "="*40)
        print("生成综合对比报告图")
        print("="*40)
        
        avg_thickness = (results['10deg']['thickness'] + results['15deg']['thickness']) / 2
        summary_save_path = os.path.join(output_dir, f"problem1_summary_report_avg_{avg_thickness:.1f}um")
        create_summary_plot(results, save_path=summary_save_path)
    
    print("\n" + "="*60)
    print("问题1 分析完成！")
    print(f"所有结果图片已保存到：{output_dir} 文件夹")
    print("="*60)
    
    return results


if __name__ == "__main__":
    # 运行主程序
    results = main()
