# -*- coding: utf-8 -*-
"""
优化方案测试脚本

用于快速验证优化代码的功能和效果
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from problem1_optimized_solution import (
    OptimizedThicknessCalculator, 
    RefractiveIndexModel,
    load_spectral_data,
    plot_optimized_results
)

def test_refractive_index_model():
    """测试折射率模型"""
    print("="*50)
    print("测试1: 折射率模型")
    print("="*50)
    
    ri_model = RefractiveIndexModel()
    
    # 测试波长范围
    wavelengths = np.linspace(8, 12, 100)  # 8-12 μm
    
    # 测试不同载流子浓度
    concentrations = [0, 1e16, 5e16, 1e17]
    
    plt.figure(figsize=(12, 8))
    
    for i, N in enumerate(concentrations):
        n_values = ri_model.refractive_index_with_carriers(wavelengths, N)
        plt.subplot(2, 2, i+1)
        plt.plot(wavelengths, n_values, 'b-', linewidth=2)
        plt.title(f'载流子浓度: {N:.0e} cm⁻³')
        plt.xlabel('波长 (μm)')
        plt.ylabel('折射率')
        plt.grid(True, alpha=0.3)
        
        # 显示折射率范围
        print(f"载流子浓度 {N:.0e} cm⁻³:")
        print(f"  折射率范围: {n_values.min():.4f} - {n_values.max():.4f}")
        print(f"  平均折射率: {n_values.mean():.4f}")
    
    plt.tight_layout()
    plt.savefig('test_refractive_index_model.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ 折射率模型测试完成")
    return True

def test_optimization_algorithm():
    """测试优化算法"""
    print("\n" + "="*50)
    print("测试2: 优化算法")
    print("="*50)
    
    # 检查数据文件
    data_files = {
        '10deg': os.path.join('data', '附件1.csv'),
        '15deg': os.path.join('data', '附件2.csv')
    }
    
    calculator = OptimizedThicknessCalculator()
    results = {}
    
    for angle, file_path in data_files.items():
        if not os.path.exists(file_path):
            print(f"警告: 数据文件 {file_path} 不存在")
            continue
        
        print(f"\n测试 {angle} 数据...")
        
        # 加载数据
        wavenumber, reflectance = load_spectral_data(file_path)
        if wavenumber is None:
            print(f"错误: 无法加载 {file_path}")
            continue
        
        # 执行优化计算
        theta_deg = 10.0 if angle == '10deg' else 15.0
        result = calculator.calculate_thickness_iterative(
            wavenumber, reflectance, theta_deg)
        
        results[angle] = result
        
        # 显示结果
        print(f"  厚度: {result['thickness_um']:.3f} μm")
        print(f"  载流子浓度: {result['carrier_concentration']:.2e} cm⁻³")
        print(f"  平均折射率: {result['average_refractive_index']:.4f}")
        print(f"  优化成功: {result['optimization_success']}")
    
    # 对比分析
    if len(results) == 2:
        print(f"\n对比分析:")
        thickness_10 = results['10deg']['thickness_um']
        thickness_15 = results['15deg']['thickness_um']
        avg_thickness = (thickness_10 + thickness_15) / 2
        relative_error = abs(thickness_10 - thickness_15) / avg_thickness * 100
        
        print(f"  平均厚度: {avg_thickness:.3f} μm")
        print(f"  相对误差: {relative_error:.3f} %")
        
        if relative_error < 1.0:
            print("  ✓ 精度评估: 优秀")
        elif relative_error < 5.0:
            print("  ✓ 精度评估: 良好")
        else:
            print("  ⚠ 精度评估: 需改进")
    
    print("✓ 优化算法测试完成")
    return results

def test_comparison_with_traditional():
    """与传统方法对比测试"""
    print("\n" + "="*50)
    print("测试3: 与传统方法对比")
    print("="*50)
    
    # 传统方法参数
    traditional_n = 2.58
    
    # 测试数据
    file_path = os.path.join('data', '附件1.csv')
    if not os.path.exists(file_path):
        print("警告: 测试数据文件不存在")
        return False
    
    wavenumber, reflectance = load_spectral_data(file_path)
    if wavenumber is None:
        print("错误: 无法加载测试数据")
        return False
    
    # 优化方法
    calculator = OptimizedThicknessCalculator()
    optimized_result = calculator.calculate_thickness_iterative(
        wavenumber, reflectance, 10.0)
    
    # 传统方法（简化计算）
    from problem1_optimized_solution import fft, fftfreq
    from scipy.interpolate import interp1d
    
    # 数据预处理
    interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                          bounds_error=False, fill_value='extrapolate')
    uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), 2**16)
    uniform_reflectance = interp_func(uniform_wavenumber)
    
    # FFT分析
    N = len(uniform_wavenumber)
    wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
    reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
    reflectance_fft = fft(reflectance_centered)
    fft_magnitude = np.abs(reflectance_fft)
    opd_axis = fftfreq(N, d=wavenumber_step)
    positive_opd_axis = opd_axis[:N // 2]
    positive_fft_magnitude = fft_magnitude[:N // 2]
    start_idx = max(1, int(0.001 * N))
    peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
    opd_traditional = positive_opd_axis[peak_index]
    
    # 传统厚度计算
    theta_i_rad = np.deg2rad(10.0)
    thickness_traditional = opd_traditional / (2 * np.sqrt(traditional_n**2 - np.sin(theta_i_rad)**2)) * 1e4
    
    # 结果对比
    print(f"传统方法:")
    print(f"  使用固定折射率: {traditional_n}")
    print(f"  计算厚度: {thickness_traditional:.3f} μm")
    
    print(f"\n优化方法:")
    print(f"  波长依赖折射率: {optimized_result['average_refractive_index']:.4f}")
    print(f"  计算厚度: {optimized_result['thickness_um']:.3f} μm")
    print(f"  载流子浓度: {optimized_result['carrier_concentration']:.2e} cm⁻³")
    
    # 改进分析
    thickness_diff = abs(optimized_result['thickness_um'] - thickness_traditional)
    improvement_percent = thickness_diff / thickness_traditional * 100
    
    print(f"\n改进分析:")
    print(f"  厚度差异: {thickness_diff:.3f} μm")
    print(f"  相对差异: {improvement_percent:.2f} %")
    print(f"  折射率修正: {optimized_result['average_refractive_index'] - traditional_n:.4f}")
    
    print("✓ 对比测试完成")
    return True

def test_parameter_sensitivity():
    """参数敏感性测试"""
    print("\n" + "="*50)
    print("测试4: 参数敏感性分析")
    print("="*50)
    
    ri_model = RefractiveIndexModel()
    
    # 测试参数范围
    wavelength = 10.0  # μm
    carrier_concentrations = np.logspace(15, 18, 20)  # 10^15 to 10^18 cm^-3
    
    # 计算折射率变化
    n_values = []
    for N in carrier_concentrations:
        n = ri_model.refractive_index_with_carriers(wavelength, N)
        n_values.append(n)
    
    n_values = np.array(n_values)
    
    # 分析敏感性
    n_intrinsic = ri_model.intrinsic_refractive_index(wavelength)
    relative_change = (n_values - n_intrinsic) / n_intrinsic * 100
    
    print(f"在波长 {wavelength} μm 处:")
    print(f"  本征折射率: {n_intrinsic:.4f}")
    print(f"  载流子浓度 1e16 cm⁻³ 时折射率: {n_values[5]:.4f}")
    print(f"  载流子浓度 1e17 cm⁻³ 时折射率: {n_values[10]:.4f}")
    print(f"  最大相对变化: {relative_change.min():.2f} %")
    
    # 绘制敏感性曲线
    plt.figure(figsize=(10, 6))
    plt.semilogx(carrier_concentrations, n_values, 'b-', linewidth=2, marker='o')
    plt.axhline(n_intrinsic, color='r', linestyle='--', label=f'本征值: {n_intrinsic:.4f}')
    plt.xlabel('载流子浓度 (cm⁻³)')
    plt.ylabel('折射率')
    plt.title(f'折射率对载流子浓度的敏感性 (λ = {wavelength} μm)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.savefig('test_parameter_sensitivity.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✓ 参数敏感性测试完成")
    return True

def run_all_tests():
    """运行所有测试"""
    print("开始运行优化方案测试套件...")
    print("测试时间可能需要几分钟，请耐心等待...")
    
    test_results = {}
    
    try:
        # 测试1: 折射率模型
        test_results['refractive_index'] = test_refractive_index_model()
        
        # 测试2: 优化算法
        test_results['optimization'] = test_optimization_algorithm()
        
        # 测试3: 与传统方法对比
        test_results['comparison'] = test_comparison_with_traditional()
        
        # 测试4: 参数敏感性
        test_results['sensitivity'] = test_parameter_sensitivity()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        return False
    
    # 测试总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20s}: {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！优化方案工作正常。")
        return True
    else:
        print("⚠ 部分测试失败，请检查环境配置和数据文件。")
        return False

if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 运行测试
    success = run_all_tests()
    
    if success:
        print("\n建议下一步:")
        print("1. 运行 python problem1_optimized_solution.py 进行完整分析")
        print("2. 查看生成的结果图片和报告")
        print("3. 阅读优化方案详细文档了解更多信息")
    else:
        print("\n故障排除建议:")
        print("1. 检查 data/ 文件夹中是否有数据文件")
        print("2. 确认所有依赖包已正确安装")
        print("3. 查看错误信息并根据提示解决问题")
