============================================================
问题1：碳化硅外延层厚度确定
基于红外干涉法的FFT算法实现
============================================================

使用的材料参数：
碳化硅(SiC)平均折射率：2.58

==================================================
正在处理附件1 (入射角 10°)
OPD分辨率：0.00027774 cm
厚度计算：
  光程差 L = 0.018331 cm
  折射率 n1 = 2.58
  入射角 θi = 10.0°
  计算厚度 d = 35.605 μm

✓ 10°入射角分析完成
  光程差：0.018331 cm
  外延层厚度：35.605 μm

==================================================
正在处理附件2 (入射角 15°)
==================================================
成功使用 gbk 编码读取文件
文件 附件2.csv 的列名: ['波数 (cm-1)', '反射率 (%)']
成功加载数据：7469 个数据点
波数范围：399.67 - 4000.12 cm⁻¹
反射率范围：0.00 - 102.74 %
插值完成：65536 个均匀数据点
波数步长：0.0549 cm⁻¹
FFT分析完成
检测到的主峰位置：0.018331 cm
主峰幅度：13138.26
OPD分辨率：0.00027774 cm
厚度计算：
  光程差 L = 0.018331 cm
  折射率 n1 = 2.58
  入射角 θi = 15.0°
  计算厚度 d = 35.705 μm

✓ 15°入射角分析完成
  光程差：0.018331 cm
10°入射角计算厚度：35.605 μm
15°入射角计算厚度：35.705 μm
平均厚度：35.655 μm
相对误差：0.28 %

✓ 结果可靠性评估：优秀（相对误差 < 5%）

========================================
生成可视化图表并保存到本地
========================================
创建输出目录：results
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:259: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  plt.tight_layout()
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:270: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  plt.savefig(save_path + '.png', dpi=300, bbox_inches='tight',
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:274: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  plt.savefig(save_path + '.pdf', bbox_inches='tight',
图片已保存：
  PNG格式：results\problem1_analysis_10deg_thickness_35.6um.png
  PDF格式：results\problem1_analysis_10deg_thickness_35.6um.pdf
D:\python3.11\Lib\tkinter\__init__.py:861: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  func(*args)
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:259: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  plt.tight_layout()
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:270: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  plt.savefig(save_path + '.png', dpi=300, bbox_inches='tight',
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:274: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  plt.savefig(save_path + '.pdf', bbox_inches='tight',
图片已保存：
  PNG格式：results\problem1_analysis_15deg_thickness_35.7um.png
  PDF格式：results\problem1_analysis_15deg_thickness_35.7um.pdf
D:\python3.11\Lib\tkinter\__init__.py:861: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  func(*args)

========================================
生成综合对比报告图
========================================
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:387: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  plt.tight_layout()
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:396: UserWarning: GlINUS}) missing from font(s) Microsoft YaHei.
  plt.savefig(save_path + '.png', dpi=300, bbox_inches='tight',
C:\Users\<USER>\Desktop\国赛代码\代码\problem1_solution.py:398: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  plt.savefig(save_path + '.pdf', bbox_inches='tight',
综合报告图已保存：
  PNG格式：results\problem1_summary_report_avg_35.7um.png
  PDF格式：results\problem1_summary_report_avg_35.7um.pdf
D:\python3.11\Lib\tkinter\__init__.py:861: UserWarning: Glyph 8315 (\N{SUPERSCRIPT MINUS}) missing from font(s) Microsoft YaHei.
  func(*args)

============================================================
问题1 分析完成！
所有结果图片已保存到：results 文件夹
============================================================