# -*- coding: utf-8 -*-
"""
问题1：碳化硅外延层厚度确定的优化数学建模与算法实现

基于红外干涉法的双光束干涉原理，考虑折射率的波长依赖性和载流子浓度效应，
使用快速傅里叶变换(FFT)算法和迭代优化方法从干涉光谱数据中提取更精确的
外延层厚度信息。

核心优化：
1. 波长依赖的折射率模型（Sellmeier方程）
2. 自由载流子效应修正（Drude模型）
3. 载流子浓度参数估计
4. 迭代优化算法
5. 不确定度分析

数学模型：
n(λ, N) = n₀(λ) * [1 - ωₚ²(N)/(ω² + γ²)]^(1/2)
其中：
- n₀(λ): 本征折射率（Sellmeier方程）
- ωₚ(N): 等离子体频率，依赖于载流子浓度N
- ω: 光频率
- γ: 阻尼常数
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
from scipy.fft import fft, fftfreq
from scipy.optimize import minimize_scalar, minimize
from scipy.constants import c, e, epsilon_0, m_e
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体显示
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class RefractiveIndexModel:
    """
    碳化硅折射率模型类
    
    实现波长依赖的本征折射率（Sellmeier方程）和
    自由载流子效应修正（Drude模型）
    """
    
    def __init__(self):
        # SiC的Sellmeier方程参数（红外波段）
        # 参考文献：Shaffer et al., Optical Materials Express (2020)
        self.A = 6.7  # 常数项
        self.B1 = 1.73  # 第一个振荡器强度
        self.C1 = 0.256  # 第一个振荡器波长 (μm²)
        self.B2 = 0.32  # 第二个振荡器强度
        self.C2 = 1250.0  # 第二个振荡器波长 (μm²)
        
        # 载流子效应参数
        self.m_eff = 0.67 * m_e  # SiC中电子有效质量
        self.gamma = 1e13  # 阻尼常数 (rad/s)
        
    def intrinsic_refractive_index(self, wavelength_um):
        """
        计算本征折射率（不考虑载流子效应）
        
        参数:
        wavelength_um (float or array): 波长，单位微米
        
        返回:
        float or array: 本征折射率
        """
        lambda_sq = wavelength_um**2
        n_sq = self.A + (self.B1 * lambda_sq) / (lambda_sq - self.C1) + \
               (self.B2 * lambda_sq) / (lambda_sq - self.C2)
        return np.sqrt(np.maximum(n_sq, 1.0))  # 确保折射率为正
    
    def plasma_frequency(self, carrier_concentration):
        """
        计算等离子体频率
        
        参数:
        carrier_concentration (float): 载流子浓度，单位 cm⁻³
        
        返回:
        float: 等离子体频率，单位 rad/s
        """
        N_m3 = carrier_concentration * 1e6  # 转换为 m⁻³
        omega_p_sq = N_m3 * e**2 / (epsilon_0 * self.m_eff)
        return np.sqrt(omega_p_sq)
    
    def refractive_index_with_carriers(self, wavelength_um, carrier_concentration):
        """
        计算考虑载流子效应的折射率
        
        参数:
        wavelength_um (float or array): 波长，单位微米
        carrier_concentration (float): 载流子浓度，单位 cm⁻³
        
        返回:
        float or array: 修正后的折射率
        """
        # 本征折射率
        n0 = self.intrinsic_refractive_index(wavelength_um)
        
        if carrier_concentration <= 0:
            return n0
        
        # 光频率
        omega = 2 * np.pi * c / (wavelength_um * 1e-6)
        
        # 等离子体频率
        omega_p = self.plasma_frequency(carrier_concentration)
        
        # Drude模型修正
        epsilon_correction = -omega_p**2 / (omega**2 + 1j * self.gamma * omega)
        epsilon_total = n0**2 + epsilon_correction
        
        # 计算复折射率的实部
        n_real = np.sqrt((np.abs(epsilon_total) + np.real(epsilon_total)) / 2)
        
        return np.maximum(n_real, 1.0)  # 确保折射率为正


class CarrierConcentrationEstimator:
    """
    载流子浓度估计器
    
    通过分析光谱特征估计掺杂载流子浓度
    """
    
    def __init__(self, refractive_index_model):
        self.ri_model = refractive_index_model
    
    def estimate_from_spectrum_shape(self, wavenumber, reflectance):
        """
        从光谱形状估计载流子浓度
        
        参数:
        wavenumber (array): 波数，单位 cm⁻¹
        reflectance (array): 反射率，单位 %
        
        返回:
        float: 估计的载流子浓度，单位 cm⁻³
        """
        # 转换波数到波长
        wavelength_um = 1e4 / wavenumber  # cm⁻¹ to μm
        
        # 分析光谱的斜率变化来估计载流子浓度
        # 高载流子浓度会导致长波段反射率下降
        
        # 选择长波段进行分析（低波数区域）
        long_wave_mask = wavenumber < 800  # cm⁻¹
        if np.sum(long_wave_mask) < 10:
            return 1e16  # 默认值
        
        long_wave_reflectance = reflectance[long_wave_mask]
        long_wave_wavenumber = wavenumber[long_wave_mask]
        
        # 计算反射率的变化率
        if len(long_wave_reflectance) > 1:
            slope = np.polyfit(long_wave_wavenumber, long_wave_reflectance, 1)[0]
            
            # 根据斜率估计载流子浓度
            # 负斜率越大，载流子浓度越高
            if slope < -0.01:
                estimated_N = 5e17  # 高掺杂
            elif slope < -0.005:
                estimated_N = 1e17  # 中等掺杂
            else:
                estimated_N = 1e16  # 低掺杂
        else:
            estimated_N = 1e16
        
        return estimated_N


class OptimizedThicknessCalculator:
    """
    优化的厚度计算器
    
    使用波长依赖的折射率模型和迭代优化算法
    """
    
    def __init__(self):
        self.ri_model = RefractiveIndexModel()
        self.carrier_estimator = CarrierConcentrationEstimator(self.ri_model)
    
    def calculate_thickness_iterative(self, wavenumber, reflectance, theta_i_deg, 
                                    initial_carrier_concentration=None):
        """
        迭代计算厚度和载流子浓度
        
        参数:
        wavenumber (array): 波数，单位 cm⁻¹
        reflectance (array): 反射率，单位 %
        theta_i_deg (float): 入射角，单位度
        initial_carrier_concentration (float): 初始载流子浓度估计
        
        返回:
        dict: 包含厚度、载流子浓度、不确定度等信息的字典
        """
        # 初始载流子浓度估计
        if initial_carrier_concentration is None:
            N_carrier = self.carrier_estimator.estimate_from_spectrum_shape(wavenumber, reflectance)
        else:
            N_carrier = initial_carrier_concentration
        
        print(f"初始载流子浓度估计: {N_carrier:.2e} cm⁻³")
        
        # 预处理数据
        uniform_wavenumber, uniform_reflectance = self._preprocess_data(wavenumber, reflectance)
        
        # FFT分析获取初始光程差
        opd_initial, opd_axis, fft_magnitude = self._calculate_opd_from_fft(
            uniform_wavenumber, uniform_reflectance)
        
        print(f"初始光程差: {opd_initial:.6f} cm")
        
        # 迭代优化
        best_result = self._iterative_optimization(
            uniform_wavenumber, uniform_reflectance, opd_initial, 
            theta_i_deg, N_carrier)
        
        # 添加FFT分析结果
        best_result['fft_data'] = {
            'opd_axis': opd_axis,
            'fft_magnitude': fft_magnitude,
            'uniform_wavenumber': uniform_wavenumber,
            'uniform_reflectance': uniform_reflectance
        }
        
        return best_result
    
    def _preprocess_data(self, wavenumber, reflectance, num_points=2**16):
        """数据预处理"""
        interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                              bounds_error=False, fill_value='extrapolate')
        uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), num_points)
        uniform_reflectance = interp_func(uniform_wavenumber)
        return uniform_wavenumber, uniform_reflectance
    
    def _calculate_opd_from_fft(self, uniform_wavenumber, uniform_reflectance):
        """FFT分析计算光程差"""
        N = len(uniform_wavenumber)
        wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
        
        reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
        reflectance_fft = fft(reflectance_centered)
        fft_magnitude = np.abs(reflectance_fft)
        
        opd_axis = fftfreq(N, d=wavenumber_step)
        positive_opd_axis = opd_axis[:N // 2]
        positive_fft_magnitude = fft_magnitude[:N // 2]
        
        start_idx = max(1, int(0.001 * N))
        peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
        opd_value = positive_opd_axis[peak_index]
        
        return opd_value, positive_opd_axis, positive_fft_magnitude
    
    def _iterative_optimization(self, wavenumber, reflectance, opd_initial, 
                               theta_i_deg, N_initial):
        """迭代优化算法"""
        theta_i_rad = np.deg2rad(theta_i_deg)
        wavelength_um = 1e4 / wavenumber
        
        # 定义目标函数
        def objective_function(params):
            thickness_cm, log_N_carrier = params
            N_carrier = 10**log_N_carrier
            
            # 计算波长依赖的折射率
            n_lambda = self.ri_model.refractive_index_with_carriers(
                wavelength_um, N_carrier)
            
            # 计算平均折射率（加权平均）
            weights = np.abs(reflectance - np.mean(reflectance))
            n_avg = np.average(n_lambda, weights=weights)
            
            # 计算理论光程差
            opd_theory = 2 * thickness_cm * np.sqrt(n_avg**2 - np.sin(theta_i_rad)**2)
            
            # 目标：最小化理论值与FFT测量值的差异
            return (opd_theory - opd_initial)**2
        
        # 设置优化边界
        thickness_bounds = (1e-6, 1e-3)  # 1μm to 1mm
        log_N_bounds = (14, 19)  # 10^14 to 10^19 cm⁻³
        
        # 初始猜测
        initial_thickness = opd_initial / (2 * 2.58)  # 使用平均折射率的粗估计
        initial_log_N = np.log10(N_initial)
        
        # 执行优化
        result = minimize(objective_function, 
                         x0=[initial_thickness, initial_log_N],
                         bounds=[thickness_bounds, log_N_bounds],
                         method='L-BFGS-B')
        
        if result.success:
            optimal_thickness_cm, optimal_log_N = result.x
            optimal_N_carrier = 10**optimal_log_N
            optimal_thickness_um = optimal_thickness_cm * 1e4
            
            # 计算最终的平均折射率
            n_final = self.ri_model.refractive_index_with_carriers(
                wavelength_um, optimal_N_carrier)
            n_avg_final = np.average(n_final, weights=np.abs(reflectance - np.mean(reflectance)))
            
            print(f"优化完成:")
            print(f"  最优厚度: {optimal_thickness_um:.3f} μm")
            print(f"  最优载流子浓度: {optimal_N_carrier:.2e} cm⁻³")
            print(f"  平均折射率: {n_avg_final:.4f}")
            
            return {
                'thickness_um': optimal_thickness_um,
                'carrier_concentration': optimal_N_carrier,
                'average_refractive_index': n_avg_final,
                'opd_measured': opd_initial,
                'opd_calculated': 2 * optimal_thickness_cm * np.sqrt(n_avg_final**2 - np.sin(theta_i_rad)**2),
                'optimization_success': True,
                'wavelength_dependent_n': n_final,
                'wavelength_um': wavelength_um
            }
        else:
            print("优化失败，使用初始估计值")
            # 使用固定折射率的传统方法作为备选
            n_avg = 2.58
            thickness_cm = opd_initial / (2 * np.sqrt(n_avg**2 - np.sin(theta_i_rad)**2))
            thickness_um = thickness_cm * 1e4
            
            return {
                'thickness_um': thickness_um,
                'carrier_concentration': N_initial,
                'average_refractive_index': n_avg,
                'opd_measured': opd_initial,
                'opd_calculated': opd_initial,
                'optimization_success': False,
                'wavelength_dependent_n': np.full_like(wavelength_um, n_avg),
                'wavelength_um': wavelength_um
            }


def load_spectral_data(file_path: str) -> tuple:
    """
    从CSV文件中加载光谱数据（保持与原版本兼容）
    """
    try:
        encodings = ['gbk', 'gb2312', 'utf-8', 'latin-1']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            raise ValueError("无法使用任何编码读取文件")
        
        columns = df.columns.tolist()
        print(f"文件 {os.path.basename(file_path)} 的列名: {columns}")
        
        wavenumber = df.iloc[:, 0].to_numpy()
        reflectance = df.iloc[:, 1].to_numpy()
        
        valid_mask = ~(np.isnan(wavenumber) | np.isnan(reflectance))
        wavenumber = wavenumber[valid_mask]
        reflectance = reflectance[valid_mask]
        
        print(f"成功加载数据：{len(wavenumber)} 个数据点")
        print(f"波数范围：{wavenumber.min():.2f} - {wavenumber.max():.2f} cm⁻¹")
        print(f"反射率范围：{reflectance.min():.2f} - {reflectance.max():.2f} %")
        
        return wavenumber, reflectance
        
    except Exception as e:
        print(f"错误：无法读取文件 {file_path}")
        print(f"错误信息：{e}")
        return None, None


def plot_optimized_results(original_data, optimized_results, title_suffix="", save_path=None):
    """
    绘制优化分析结果的可视化图表

    参数:
    original_data: 原始数据 (wavenumber, reflectance)
    optimized_results: 优化计算结果字典
    title_suffix: 图表标题后缀
    save_path: 图片保存路径（不含扩展名）
    """
    wavenumber, reflectance = original_data
    fft_data = optimized_results['fft_data']

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'问题1：碳化硅外延层厚度优化分析{title_suffix}', fontsize=16, fontweight='bold')

    # 子图1: 原始光谱
    axes[0, 0].plot(wavenumber, reflectance, 'b-', linewidth=1, label='原始数据')
    axes[0, 0].set_title('原始干涉光谱')
    axes[0, 0].set_xlabel('波数 (cm⁻¹)')
    axes[0, 0].set_ylabel('反射率 (%)')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].legend()

    # 子图2: 波长依赖的折射率
    wavelength_um = optimized_results['wavelength_um']
    n_lambda = optimized_results['wavelength_dependent_n']

    axes[0, 1].plot(wavelength_um, n_lambda, 'r-', linewidth=2, label='优化折射率')
    axes[0, 1].axhline(optimized_results['average_refractive_index'],
                      color='g', linestyle='--', label=f'平均值: {optimized_results["average_refractive_index"]:.4f}')
    axes[0, 1].set_title('波长依赖的折射率')
    axes[0, 1].set_xlabel('波长 (μm)')
    axes[0, 1].set_ylabel('折射率')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].legend()

    # 子图3: FFT幅度谱
    opd_axis = fft_data['opd_axis']
    fft_magnitude = fft_data['fft_magnitude']
    opd_measured = optimized_results['opd_measured']

    axes[0, 2].plot(opd_axis, fft_magnitude, 'g-', linewidth=1)
    axes[0, 2].axvline(opd_measured, color='r', linestyle='--', linewidth=2,
                      label=f'测量OPD = {opd_measured:.4f} cm')
    axes[0, 2].set_title('FFT幅度谱（光程差域）')
    axes[0, 2].set_xlabel('光程差 (cm)')
    axes[0, 2].set_ylabel('幅度')
    axes[0, 2].grid(True, alpha=0.3)
    axes[0, 2].legend()

    # 子图4: 载流子浓度效应
    ri_model = RefractiveIndexModel()
    n_intrinsic = ri_model.intrinsic_refractive_index(wavelength_um)

    axes[1, 0].plot(wavelength_um, n_intrinsic, 'b--', linewidth=2, label='本征折射率')
    axes[1, 0].plot(wavelength_um, n_lambda, 'r-', linewidth=2, label='含载流子效应')
    axes[1, 0].set_title('载流子效应对折射率的影响')
    axes[1, 0].set_xlabel('波长 (μm)')
    axes[1, 0].set_ylabel('折射率')
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].legend()

    # 子图5: 优化结果对比
    methods = ['传统方法\n(固定折射率)', '优化方法\n(波长依赖)']
    # 计算传统方法结果作为对比
    traditional_n = 2.58
    traditional_thickness = optimized_results['opd_measured'] / (2 * traditional_n) * 1e4
    thicknesses = [traditional_thickness, optimized_results['thickness_um']]
    colors = ['lightblue', 'orange']

    bars = axes[1, 1].bar(methods, thicknesses, color=colors, alpha=0.7, width=0.6)
    axes[1, 1].set_title('厚度计算结果对比')
    axes[1, 1].set_ylabel('厚度 (μm)')
    axes[1, 1].grid(True, alpha=0.3, axis='y')

    # 在柱状图上标注数值
    for bar, thickness in zip(bars, thicknesses):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{thickness:.3f} μm', ha='center', va='bottom', fontweight='bold')

    # 子图6: 参数信息
    axes[1, 2].axis('off')
    info_text = f"""优化结果摘要:

厚度: {optimized_results['thickness_um']:.3f} μm
载流子浓度: {optimized_results['carrier_concentration']:.2e} cm⁻³
平均折射率: {optimized_results['average_refractive_index']:.4f}

测量光程差: {optimized_results['opd_measured']:.6f} cm
计算光程差: {optimized_results['opd_calculated']:.6f} cm
相对误差: {abs(optimized_results['opd_measured'] - optimized_results['opd_calculated'])/optimized_results['opd_measured']*100:.3f}%

优化状态: {'成功' if optimized_results['optimization_success'] else '失败'}
"""

    axes[1, 2].text(0.1, 0.9, info_text, transform=axes[1, 2].transAxes,
                    fontsize=11, verticalalignment='top', fontfamily='monospace',
                    bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

    plt.tight_layout()

    # 保存图片
    if save_path:
        try:
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)

            plt.savefig(save_path + '.png', dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.savefig(save_path + '.pdf', bbox_inches='tight',
                       facecolor='white', edgecolor='none')

            print(f"优化分析图已保存：")
            print(f"  PNG格式：{save_path}.png")
            print(f"  PDF格式：{save_path}.pdf")

        except Exception as e:
            print(f"保存图片失败：{e}")

    plt.show()


def create_optimization_comparison_plot(results_10deg, results_15deg, save_path=None):
    """
    创建优化方法与传统方法的对比图表

    参数:
    results_10deg: 10度入射角的优化结果
    results_15deg: 15度入射角的优化结果
    save_path: 保存路径（不含扩展名）
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('问题1：优化方法 vs 传统方法 - 综合对比分析', fontsize=16, fontweight='bold')

    # 计算传统方法结果
    traditional_n = 2.58
    traditional_thickness_10 = results_10deg['opd_measured'] / (2 * traditional_n) * 1e4
    traditional_thickness_15 = results_15deg['opd_measured'] / (2 * traditional_n) * 1e4

    # 子图1: 厚度结果对比
    angles = ['10°', '15°']
    traditional_thicknesses = [traditional_thickness_10, traditional_thickness_15]
    optimized_thicknesses = [results_10deg['thickness_um'], results_15deg['thickness_um']]

    x = np.arange(len(angles))
    width = 0.35

    bars1 = axes[0, 0].bar(x - width/2, traditional_thicknesses, width,
                          label='传统方法', color='lightblue', alpha=0.7)
    bars2 = axes[0, 0].bar(x + width/2, optimized_thicknesses, width,
                          label='优化方法', color='orange', alpha=0.7)

    axes[0, 0].set_title('厚度计算结果对比')
    axes[0, 0].set_ylabel('厚度 (μm)')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(angles)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3, axis='y')

    # 在柱状图上标注数值
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 0.05,
                           f'{height:.3f}', ha='center', va='bottom', fontsize=9)

    # 子图2: 折射率对比
    wavelength_10 = results_10deg['wavelength_um']
    wavelength_15 = results_15deg['wavelength_um']
    n_10 = results_10deg['wavelength_dependent_n']
    n_15 = results_15deg['wavelength_dependent_n']

    axes[0, 1].plot(wavelength_10, n_10, 'b-', linewidth=2, label='10° 优化折射率', alpha=0.8)
    axes[0, 1].plot(wavelength_15, n_15, 'r-', linewidth=2, label='15° 优化折射率', alpha=0.8)
    axes[0, 1].axhline(traditional_n, color='g', linestyle='--', linewidth=2,
                      label=f'传统固定值: {traditional_n}')
    axes[0, 1].set_title('折射率模型对比')
    axes[0, 1].set_xlabel('波长 (μm)')
    axes[0, 1].set_ylabel('折射率')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 子图3: 载流子浓度
    carrier_concentrations = [results_10deg['carrier_concentration'],
                             results_15deg['carrier_concentration']]

    bars = axes[1, 0].bar(angles, carrier_concentrations, color=['blue', 'red'], alpha=0.7)
    axes[1, 0].set_title('估计的载流子浓度')
    axes[1, 0].set_ylabel('载流子浓度 (cm⁻³)')
    axes[1, 0].set_yscale('log')
    axes[1, 0].grid(True, alpha=0.3, axis='y')

    # 标注数值
    for bar, conc in zip(bars, carrier_concentrations):
        height = bar.get_height()
        axes[1, 0].text(bar.get_x() + bar.get_width()/2., height * 1.1,
                       f'{conc:.1e}', ha='center', va='bottom', fontsize=9)

    # 子图4: 精度分析
    traditional_avg = (traditional_thickness_10 + traditional_thickness_15) / 2
    optimized_avg = (results_10deg['thickness_um'] + results_15deg['thickness_um']) / 2

    traditional_error = abs(traditional_thickness_10 - traditional_thickness_15) / traditional_avg * 100
    optimized_error = abs(results_10deg['thickness_um'] - results_15deg['thickness_um']) / optimized_avg * 100

    methods = ['传统方法', '优化方法']
    errors = [traditional_error, optimized_error]
    colors = ['lightblue', 'orange']

    bars = axes[1, 1].bar(methods, errors, color=colors, alpha=0.7)
    axes[1, 1].set_title('方法精度对比（相对误差）')
    axes[1, 1].set_ylabel('相对误差 (%)')
    axes[1, 1].axhline(5.0, color='red', linestyle='--', alpha=0.7, label='5% 警戒线')
    axes[1, 1].axhline(1.0, color='green', linestyle='--', alpha=0.7, label='1% 优秀线')
    axes[1, 1].grid(True, alpha=0.3, axis='y')
    axes[1, 1].legend()

    # 标注数值
    for bar, error in zip(bars, errors):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       f'{error:.3f}%', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()

    # 保存对比图
    if save_path:
        try:
            save_dir = os.path.dirname(save_path)
            if save_dir and not os.path.exists(save_dir):
                os.makedirs(save_dir)

            plt.savefig(save_path + '.png', dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.savefig(save_path + '.pdf', bbox_inches='tight',
                       facecolor='white', edgecolor='none')

            print(f"对比分析图已保存：")
            print(f"  PNG格式：{save_path}.png")
            print(f"  PDF格式：{save_path}.pdf")

        except Exception as e:
            print(f"保存对比图失败：{e}")

    plt.show()


def main_optimized():
    """
    优化版本的主程序
    """
    print("="*70)
    print("问题1：碳化硅外延层厚度确定 - 优化版本")
    print("基于波长依赖折射率模型和载流子效应修正的FFT算法实现")
    print("="*70)

    # 文件路径配置
    data_dir = "data"
    file_path_10_deg = os.path.join(data_dir, "附件1.csv")
    file_path_15_deg = os.path.join(data_dir, "附件2.csv")

    # 创建优化计算器
    calculator = OptimizedThicknessCalculator()

    # 结果存储
    optimized_results = {}

    # 处理10°入射角数据
    print("\n" + "="*60)
    print("正在处理附件1 (入射角 10°) - 优化分析")
    print("="*60)

    w_10, r_10 = load_spectral_data(file_path_10_deg)
    if w_10 is not None:
        result_10 = calculator.calculate_thickness_iterative(w_10, r_10, 10.0)
        optimized_results['10deg'] = result_10
        optimized_results['10deg']['original_data'] = (w_10, r_10)

        print(f"\n✓ 10°入射角优化分析完成")
        print(f"  优化厚度：{result_10['thickness_um']:.3f} μm")
        print(f"  载流子浓度：{result_10['carrier_concentration']:.2e} cm⁻³")
        print(f"  平均折射率：{result_10['average_refractive_index']:.4f}")

    # 处理15°入射角数据
    print("\n" + "="*60)
    print("正在处理附件2 (入射角 15°) - 优化分析")
    print("="*60)

    w_15, r_15 = load_spectral_data(file_path_15_deg)
    if w_15 is not None:
        result_15 = calculator.calculate_thickness_iterative(w_15, r_15, 15.0)
        optimized_results['15deg'] = result_15
        optimized_results['15deg']['original_data'] = (w_15, r_15)

        print(f"\n✓ 15°入射角优化分析完成")
        print(f"  优化厚度：{result_15['thickness_um']:.3f} μm")
        print(f"  载流子浓度：{result_15['carrier_concentration']:.2e} cm⁻³")
        print(f"  平均折射率：{result_15['average_refractive_index']:.4f}")

    # 优化结果分析
    if '10deg' in optimized_results and '15deg' in optimized_results:
        print("\n" + "="*70)
        print("优化结果可靠性分析")
        print("="*70)

        thickness_10_opt = optimized_results['10deg']['thickness_um']
        thickness_15_opt = optimized_results['15deg']['thickness_um']

        avg_thickness_opt = (thickness_10_opt + thickness_15_opt) / 2
        relative_error_opt = abs(thickness_10_opt - thickness_15_opt) / avg_thickness_opt * 100

        print(f"10°入射角优化厚度：{thickness_10_opt:.3f} μm")
        print(f"15°入射角优化厚度：{thickness_15_opt:.3f} μm")
        print(f"平均厚度：{avg_thickness_opt:.3f} μm")
        print(f"相对误差：{relative_error_opt:.3f} %")

        # 与传统方法对比
        traditional_n = 2.58
        traditional_10 = optimized_results['10deg']['opd_measured'] / (2 * traditional_n) * 1e4
        traditional_15 = optimized_results['15deg']['opd_measured'] / (2 * traditional_n) * 1e4
        traditional_avg = (traditional_10 + traditional_15) / 2
        traditional_error = abs(traditional_10 - traditional_15) / traditional_avg * 100

        print(f"\n传统方法对比：")
        print(f"传统方法平均厚度：{traditional_avg:.3f} μm")
        print(f"传统方法相对误差：{traditional_error:.3f} %")
        print(f"优化改进：{((traditional_error - relative_error_opt) / traditional_error * 100):.1f}% 精度提升")

    # 可视化结果
    print("\n" + "="*50)
    print("生成优化分析可视化图表")
    print("="*50)

    output_dir = "results_optimized"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录：{output_dir}")

    if '10deg' in optimized_results:
        result_10 = optimized_results['10deg']
        save_path_10 = os.path.join(output_dir,
                                   f"optimized_analysis_10deg_thickness_{result_10['thickness_um']:.1f}um")
        plot_optimized_results(result_10['original_data'], result_10,
                              " (入射角 10°)", save_path=save_path_10)

    if '15deg' in optimized_results:
        result_15 = optimized_results['15deg']
        save_path_15 = os.path.join(output_dir,
                                   f"optimized_analysis_15deg_thickness_{result_15['thickness_um']:.1f}um")
        plot_optimized_results(result_15['original_data'], result_15,
                              " (入射角 15°)", save_path=save_path_15)

    # 生成对比分析图
    if '10deg' in optimized_results and '15deg' in optimized_results:
        print("\n" + "="*50)
        print("生成优化方法对比分析图")
        print("="*50)

        comparison_save_path = os.path.join(output_dir, "optimization_comparison_report")
        create_optimization_comparison_plot(optimized_results['10deg'],
                                          optimized_results['15deg'],
                                          save_path=comparison_save_path)

    print("\n" + "="*70)
    print("问题1 优化分析完成！")
    print(f"所有结果图片已保存到：{output_dir} 文件夹")
    print("="*70)

    return optimized_results


if __name__ == "__main__":
    # 运行优化版本主程序
    optimized_results = main_optimized()
